#!/usr/bin/env python3
"""
Script de test pour diagnostiquer les problèmes du système de tirage sacrificiel.
Ce script simule les fonctions principales sans avoir besoin de Discord ou Google Sheets.
"""

import logging
import time
from datetime import datetime
import pytz

# Configuration du logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class MockCardsSystem:
    """Simulation du système de cartes pour les tests."""
    
    def __init__(self):
        self.cards_cache = None
        self.cards_cache_time = 0
        self._cache_lock = None
        
        # Données de test simulées
        self.mock_data = [
            ["category", "name", "user_data..."],  # Header
            ["Commune", "Carte1.png", "123456:2", "789012:1"],
            ["Commune", "Carte2.png", "123456:1"],
            ["Rare", "Carte3.png", "123456:3"],
            ["Rare", "Carte4 (Full).png", "123456:1"],  # Carte Full
            ["Épique", "Carte5.png", "123456:1"],
            ["Légendaire", "Carte6.png", "123456:1"],
        ]
        
    def refresh_cards_cache(self):
        """Simule le rafraîchissement du cache."""
        self.cards_cache = self.mock_data
        self.cards_cache_time = time.time()
        logging.info("[CACHE] Cache des cartes rechargé avec succès (simulation)")
        
    def get_user_cards(self, user_id: int):
        """Simule la récupération des cartes d'un utilisateur."""
        now = time.time()
        if not self.cards_cache or now - self.cards_cache_time > 5:
            logging.info(f"[DEBUG_CARDS] Rafraîchissement du cache pour user_id={user_id}")
            self.refresh_cards_cache()

        if not self.cards_cache:
            logging.error(f"[DEBUG_CARDS] Cache vide pour user_id={user_id}")
            return []

        rows = self.cards_cache[1:]  # Skip header
        user_cards = []
        total_rows_processed = 0
        matching_cells_found = 0
        
        logging.info(f"[DEBUG_CARDS] Traitement de {len(rows)} lignes pour user_id={user_id}")
        
        for row in rows:
            total_rows_processed += 1
            if len(row) < 3:
                continue
            cat, name = row[0], row[1]
            for cell in row[2:]:
                if not cell:
                    continue
                try:
                    uid, count = cell.split(":", 1)
                    uid = uid.strip()
                    if int(uid) == user_id:
                        matching_cells_found += 1
                        card_count = int(count)
                        user_cards.extend([(cat, name)] * card_count)
                        logging.debug(f"[DEBUG_CARDS] Trouvé {card_count}x {cat}/{name} pour user_id={user_id}")
                except (ValueError, IndexError) as e:
                    logging.warning(f"[SECURITY] Données corrompues dans get_user_cards: {cell}, erreur: {e}")
                    continue
        
        logging.info(f"[DEBUG_CARDS] Résultat pour user_id={user_id}: {len(user_cards)} cartes trouvées, {matching_cells_found} cellules correspondantes, {total_rows_processed} lignes traitées")
        return user_cards

def test_user_cards_retrieval():
    """Test de récupération des cartes utilisateur."""
    print("\n=== Test de récupération des cartes ===")
    
    system = MockCardsSystem()
    
    # Test avec un utilisateur qui a des cartes
    user_id = 123456
    user_cards = system.get_user_cards(user_id)
    print(f"Cartes pour user_id {user_id}: {len(user_cards)}")
    for cat, name in user_cards:
        print(f"  - {cat}: {name}")
    
    # Test avec un utilisateur qui n'a pas de cartes
    user_id_no_cards = 999999
    user_cards_empty = system.get_user_cards(user_id_no_cards)
    print(f"Cartes pour user_id {user_id_no_cards}: {len(user_cards_empty)}")
    
    return user_cards

def test_eligible_cards_filtering(user_cards):
    """Test du filtrage des cartes éligibles."""
    print("\n=== Test de filtrage des cartes éligibles ===")
    
    eligible_cards = [(cat, name) for cat, name in user_cards
                     if not name.removesuffix('.png').endswith(' (Full)')]
    
    print(f"Cartes totales: {len(user_cards)}")
    print(f"Cartes éligibles (hors Full): {len(eligible_cards)}")
    
    full_cards = [(cat, name) for cat, name in user_cards
                  if name.removesuffix('.png').endswith(' (Full)')]
    print(f"Cartes Full exclues: {len(full_cards)}")
    for cat, name in full_cards:
        print(f"  - {cat}: {name}")
    
    return eligible_cards

def test_sacrificial_draw_logic(eligible_cards):
    """Test de la logique de sélection pour le tirage sacrificiel."""
    print("\n=== Test de logique de tirage sacrificiel ===")
    
    if len(eligible_cards) < 5:
        print(f"❌ Pas assez de cartes éligibles: {len(eligible_cards)}/5")
        return False
    
    print(f"✅ Assez de cartes éligibles: {len(eligible_cards)}/5")
    
    # Simuler la sélection de 5 cartes
    import hashlib
    import random
    
    user_id = 123456
    paris_tz = pytz.timezone("Europe/Paris")
    today = datetime.now(paris_tz).strftime("%Y-%m-%d")
    
    # Créer une graine déterministe
    seed_string = f"{user_id}_{today}_sacrificial"
    seed = int(hashlib.md5(seed_string.encode()).hexdigest(), 16) % (2**32)
    rng = random.Random(seed)
    
    # Compter les cartes disponibles
    card_counts = {}
    for cat, name in eligible_cards:
        key = (cat, name)
        card_counts[key] = card_counts.get(key, 0) + 1
    
    print(f"Types de cartes uniques: {len(card_counts)}")
    for (cat, name), count in card_counts.items():
        print(f"  - {count}x {cat}: {name}")
    
    # Sélectionner 5 cartes
    unique_cards = list(card_counts.keys())
    rng.shuffle(unique_cards)
    
    selected_cards = []
    selected_counts = {}
    
    for card_key in unique_cards:
        if len(selected_cards) >= 5:
            break
        
        cat, name = card_key
        available_count = card_counts[card_key]
        already_selected = selected_counts.get(card_key, 0)
        
        if already_selected < available_count:
            selected_cards.append((cat, name))
            selected_counts[card_key] = already_selected + 1
    
    print(f"Cartes sélectionnées pour le sacrifice: {len(selected_cards)}")
    for cat, name in selected_cards:
        print(f"  - {cat}: {name}")
    
    return len(selected_cards) == 5

def main():
    """Fonction principale de test."""
    print("🔍 Diagnostic du système de tirage sacrificiel")
    print("=" * 50)
    
    # Test 1: Récupération des cartes
    user_cards = test_user_cards_retrieval()
    
    # Test 2: Filtrage des cartes éligibles
    eligible_cards = test_eligible_cards_filtering(user_cards)
    
    # Test 3: Logique de tirage sacrificiel
    success = test_sacrificial_draw_logic(eligible_cards)
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Tous les tests sont passés avec succès!")
    else:
        print("❌ Des problèmes ont été détectés.")
    
    print("\n🔧 Recommandations pour résoudre les problèmes:")
    print("1. Vérifiez que la variable SERVICE_ACCOUNT_JSON est correctement configurée")
    print("2. Vérifiez que Google Sheets est accessible")
    print("3. Vérifiez que les données dans Google Sheets respectent le format user_id:count")
    print("4. Utilisez la commande /debug_cartes pour diagnostiquer en temps réel")

if __name__ == "__main__":
    main()
