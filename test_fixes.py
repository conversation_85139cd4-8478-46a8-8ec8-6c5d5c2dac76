#!/usr/bin/env python3
"""
Script de test pour vérifier les corrections apportées au système de tirage sacrificiel.
"""

import logging
import time
from datetime import datetime
import pytz
import hashlib
import random

# Configuration du logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_select_daily_sacrificial_cards_fixed():
    """Test de la fonction corrigée select_daily_sacrificial_cards."""
    print("\n=== Test de la fonction select_daily_sacrificial_cards corrigée ===")
    
    def select_daily_sacrificial_cards_fixed(user_id: int, eligible_cards: list[tuple[str, str]]) -> list[tuple[str, str]]:
        """Version corrigée de la fonction."""
        # Obtenir la date actuelle en timezone Paris
        paris_tz = pytz.timezone("Europe/Paris")
        today = datetime.now(paris_tz).strftime("%Y-%m-%d")

        # Créer une seed déterministe basée sur l'utilisateur et la date
        seed_string = f"{user_id}_{today}_sacrificial"
        seed_hash = hashlib.md5(seed_string.encode()).hexdigest()
        seed = int(seed_hash[:8], 16)  # Utiliser les 8 premiers caractères hex comme seed

        # Utiliser cette seed pour la sélection aléatoire
        rng = random.Random(seed)

        # Compter les cartes disponibles par type
        card_counts = {}
        for cat, name in eligible_cards:
            key = (cat, name)
            card_counts[key] = card_counts.get(key, 0) + 1

        # Créer une liste pondérée pour la sélection
        # Chaque carte unique apparaît une fois dans la liste de sélection
        unique_cards = list(card_counts.keys())
        rng.shuffle(unique_cards)

        # Sélectionner 5 cartes en respectant les quantités disponibles
        selected_cards = []
        selected_counts = {}

        for card_key in unique_cards:
            if len(selected_cards) >= 5:
                break

            cat, name = card_key
            available_count = card_counts[card_key]
            already_selected = selected_counts.get(card_key, 0)

            if already_selected < available_count:
                selected_cards.append((cat, name))
                selected_counts[card_key] = already_selected + 1

        # Si on n'a pas assez de cartes uniques, compléter avec des doublons
        max_iterations = 10  # Protection contre les boucles infinies
        iteration_count = 0
        
        while len(selected_cards) < 5 and iteration_count < max_iterations:
            iteration_count += 1
            cards_added_this_iteration = 0
            
            for card_key in unique_cards:
                if len(selected_cards) >= 5:
                    break

                cat, name = card_key
                available_count = card_counts[card_key]
                already_selected = selected_counts.get(card_key, 0)

                if already_selected < available_count:
                    selected_cards.append((cat, name))
                    selected_counts[card_key] = already_selected + 1
                    cards_added_this_iteration += 1
            
            # Si aucune carte n'a été ajoutée dans cette itération, on ne peut pas en ajouter plus
            if cards_added_this_iteration == 0:
                break

        return selected_cards[:5]
    
    # Test 1: Cas normal avec assez de cartes
    print("Test 1: Cas normal avec assez de cartes")
    eligible_cards_normal = [
        ("Commune", "Carte1.png"), ("Commune", "Carte1.png"),
        ("Commune", "Carte2.png"),
        ("Rare", "Carte3.png"), ("Rare", "Carte3.png"), ("Rare", "Carte3.png"),
        ("Épique", "Carte5.png"),
        ("Légendaire", "Carte6.png"),
    ]
    selected = select_daily_sacrificial_cards_fixed(123456, eligible_cards_normal)
    print(f"Cartes sélectionnées: {len(selected)}")
    for cat, name in selected:
        print(f"  - {cat}: {name}")
    assert len(selected) == 5, f"Devrait sélectionner 5 cartes, mais a sélectionné {len(selected)}"
    
    # Test 2: Cas limite avec exactement 5 cartes
    print("\nTest 2: Cas limite avec exactement 5 cartes")
    eligible_cards_exact = [
        ("Commune", "Carte1.png"),
        ("Commune", "Carte2.png"),
        ("Rare", "Carte3.png"),
        ("Épique", "Carte4.png"),
        ("Légendaire", "Carte5.png"),
    ]
    selected = select_daily_sacrificial_cards_fixed(123456, eligible_cards_exact)
    print(f"Cartes sélectionnées: {len(selected)}")
    assert len(selected) == 5, f"Devrait sélectionner 5 cartes, mais a sélectionné {len(selected)}"
    
    # Test 3: Cas problématique avec moins de 5 cartes
    print("\nTest 3: Cas problématique avec moins de 5 cartes")
    eligible_cards_few = [
        ("Commune", "Carte1.png"),
        ("Rare", "Carte2.png"),
        ("Épique", "Carte3.png"),
    ]
    selected = select_daily_sacrificial_cards_fixed(123456, eligible_cards_few)
    print(f"Cartes sélectionnées: {len(selected)}")
    for cat, name in selected:
        print(f"  - {cat}: {name}")
    assert len(selected) == 3, f"Devrait sélectionner 3 cartes, mais a sélectionné {len(selected)}"
    
    # Test 4: Cas avec beaucoup de doublons
    print("\nTest 4: Cas avec beaucoup de doublons")
    eligible_cards_duplicates = [
        ("Commune", "Carte1.png")] * 10 + [("Rare", "Carte2.png")] * 5
    selected = select_daily_sacrificial_cards_fixed(123456, eligible_cards_duplicates)
    print(f"Cartes sélectionnées: {len(selected)}")
    for cat, name in selected:
        print(f"  - {cat}: {name}")
    assert len(selected) == 5, f"Devrait sélectionner 5 cartes, mais a sélectionné {len(selected)}"
    
    print("✅ Tous les tests de select_daily_sacrificial_cards sont passés!")

def test_timeout_simulation():
    """Test de simulation de timeout."""
    print("\n=== Test de simulation de timeout ===")
    
    import asyncio
    
    async def slow_function():
        """Fonction qui prend trop de temps."""
        await asyncio.sleep(2)  # Simule une opération lente
        return True
    
    async def test_with_timeout():
        """Test avec timeout."""
        try:
            result = await asyncio.wait_for(slow_function(), timeout=1.0)
            print("✅ Fonction terminée à temps")
            return result
        except asyncio.TimeoutError:
            print("❌ Timeout détecté et géré correctement")
            return False
    
    # Exécuter le test
    result = asyncio.run(test_with_timeout())
    print(f"Résultat: {result}")
    assert result == False, "Le timeout devrait être détecté"
    
    print("✅ Test de timeout réussi!")

def main():
    """Fonction principale de test."""
    print("🔧 Test des corrections du système de tirage sacrificiel")
    print("=" * 60)
    
    try:
        # Test 1: Fonction de sélection corrigée
        test_select_daily_sacrificial_cards_fixed()
        
        # Test 2: Gestion du timeout
        test_timeout_simulation()
        
        print("\n" + "=" * 60)
        print("✅ Tous les tests sont passés avec succès!")
        print("\n🔧 Corrections apportées:")
        print("1. ✅ Initialisation de sheet_sacrificial_draw dans __init__")
        print("2. ✅ Suppression de la création répétée de la feuille")
        print("3. ✅ Protection contre les boucles infinies dans select_daily_sacrificial_cards")
        print("4. ✅ Ajout d'un timeout de 30 secondes pour execute_sacrificial_draw")
        print("5. ✅ Meilleure gestion d'erreurs avec try-catch multiples")
        print("6. ✅ Logging détaillé pour le diagnostic")
        
    except Exception as e:
        print(f"\n❌ Erreur lors des tests: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
